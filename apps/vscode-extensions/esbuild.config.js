const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');

const isWatch = process.argv.includes('--watch');

// Function to get all files from a directory recursively
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      fileList = getAllFiles(filePath, fileList);
    } else if (file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  return fileList;
}

// Get all worker files
const workerFiles = getAllFiles(path.join(__dirname, 'src/workers'));

/** @type {import('esbuild').BuildOptions} */
const extensionConfig = {
  entryPoints: ['src/extension.ts'],
  bundle: true,
  platform: 'node',
  target: 'node18',
  outdir: 'dist',
  format: 'cjs',
  sourcemap: true,
  minify: process.env.NODE_ENV === 'production',
  external: ['vscode'],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
};

/** @type {import('esbuild').BuildOptions} */
const workersConfig = {
  entryPoints: workerFiles,
  bundle: true,
  platform: 'node',
  target: 'node18',
  outdir: 'dist/workers',
  outbase: 'src/workers',
  format: 'esm',
  outExtension: { '.js': '.mjs' },
  sourcemap: true,
  minify: process.env.NODE_ENV === 'production',
  external: [
    'vscode'
    // Note: All dependencies are now bundled or handled with fallbacks
  ],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
  banner: {
    js: `
import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Fallback for onnxruntime-node when not available
globalThis.__ONNX_NODE_FALLBACK__ = true;
`
  },
  plugins: [
    {
      name: 'native-fallbacks',
      setup(build) {
        // Provide a fallback for onnxruntime-node
        build.onResolve({ filter: /^onnxruntime-node$/ }, args => {
          return {
            path: args.path,
            namespace: 'onnx-fallback'
          }
        })

        // Provide a fallback for sharp
        build.onResolve({ filter: /^sharp$/ }, args => {
          return {
            path: args.path,
            namespace: 'sharp-fallback'
          }
        })

        build.onLoad({ filter: /.*/, namespace: 'onnx-fallback' }, args => {
          return {
            contents: `
              // Fallback implementation for onnxruntime-node
              console.warn('onnxruntime-node not available, using web fallback');
              export default {};
              export const InferenceSession = {};
              export const Tensor = {};
              export const env = {};
            `,
            loader: 'js'
          }
        })

        build.onLoad({ filter: /.*/, namespace: 'sharp-fallback' }, args => {
          return {
            contents: `
              // Fallback implementation for sharp
              console.warn('sharp not available, image processing disabled');
              export default function sharp() {
                throw new Error('Sharp not available in packaged extension');
              };
            `,
            loader: 'js'
          }
        })
      }
    }
  ]
};

async function build() {
  try {
    await Promise.all([
      esbuild.build(extensionConfig),
      esbuild.build(workersConfig)
    ]);

    console.log('Build completed successfully');
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

async function watch() {
  try {
    const [extensionContext, workersContext] = await Promise.all([
      esbuild.context(extensionConfig),
      esbuild.context(workersConfig)
    ]);

    await Promise.all([
      extensionContext.watch(),
      workersContext.watch()
    ]);

    console.log('Watching for changes...');
  } catch (error) {
    console.error('Watch failed:', error);
    process.exit(1);
  }
}

if (isWatch) {
  watch();
} else {
  build();
}